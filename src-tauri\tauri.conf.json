{"$schema": "https://schema.tauri.app/config/2", "productName": "PasteMe", "version": "1.2.4", "identifier": "in.ckyl.pasteme", "build": {"beforeDevCommand": "yarn dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "yarn build", "frontendDist": "../dist"}, "app": {"windows": [{"label": "main", "title": "PasteMe", "width": 800, "height": 600, "decorations": false, "resizable": false, "transparent": true, "visible": false, "url": "/"}, {"label": "context", "title": "PasteMe Context", "width": 400, "height": 476, "url": "/panel", "visible": false, "decorations": false, "resizable": false, "alwaysOnTop": true, "skipTaskbar": true, "transparent": true}], "withGlobalTauri": true, "security": {"csp": null}}, "bundle": {"active": true, "publisher": "CKylinMC", "category": "DeveloperTool", "shortDescription": "Paste Me", "longDescription": "PasteMe", "copyright": "MIT License", "createUpdaterArtifacts": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}, "plugins": {"updater": {"pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDdFRDIxOUY1RDJGMDVEQgpSV1RiQlM5ZG55SHRCeHRwd0tjMlRudjQ1UWpYaG9nL1NJQTBUM3hYS1J2M3h0dG55dTY1ckE4Ugo=", "endpoints": ["https://github.com/CKylinMC/PasteMe/releases/latest/download/latest.json", "https://gh-proxy.com/github.com/CKylinMC/PasteMe/releases/latest/download/latest.json", "https://ghfast.top/https://github.com/CKylinMC/PasteMe/releases/latest/download/latest.json"]}}}