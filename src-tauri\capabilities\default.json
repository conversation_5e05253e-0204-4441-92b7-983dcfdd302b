{"$schema": "../gen/schemas/desktop-schema.json", "identifier": "default", "description": "Capability for the main window", "windows": ["main", "context"], "permissions": ["core:default", "opener:default", "core:window:default", "core:window:allow-close", "core:window:allow-create", "core:window:allow-destroy", "core:window:allow-start-dragging", "core:window:allow-set-position", "core:window:allow-hide", "core:window:allow-show", "core:window:allow-set-title", "core:window:allow-set-size", "core:window:allow-maximize", "core:window:allow-minimize", "core:window:allow-set-always-on-top", "core:window:allow-set-skip-taskbar", "core:window:allow-set-focus", "global-shortcut:default", "global-shortcut:allow-unregister-all", "global-shortcut:allow-register", "autostart:default", "autostart:allow-enable", "autostart:allow-disable", "autostart:allow-is-enabled", "store:default", "store:allow-get", "store:allow-has", "store:allow-set", "store:allow-save", "store:allow-load", "core:path:default", "clipboard-manager:allow-read-image", "clipboard-manager:allow-read-text", "clipboard-manager:allow-write-image", "clipboard-manager:allow-write-text", "process:default", "cors-fetch:default", "updater:default", {"identifier": "http:default", "allow": [{"url": "http://*/*"}, {"url": "https://*/*"}]}]}