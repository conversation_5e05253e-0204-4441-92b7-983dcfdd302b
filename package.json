{"name": "pasteme", "private": true, "version": "1.2.4", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "tauri": "tauri", "bump": "tsx .scripts/bump.ts"}, "dependencies": {"@ai-sdk/openai": "^1.1.13", "@ai-sdk/openai-compatible": "^0.1.11", "@ai-sdk/vue": "^1.1.18", "@tailwindcss/vite": "^4.0.8", "@tauri-apps/api": "^2.2.0", "@tauri-apps/plugin-autostart": "~2.2.0", "@tauri-apps/plugin-clipboard-manager": "~2.2.1", "@tauri-apps/plugin-global-shortcut": "~2.2.0", "@tauri-apps/plugin-http": "~2.3.0", "@tauri-apps/plugin-opener": "^2.2.5", "@tauri-apps/plugin-process": "^2.2.0", "@tauri-apps/plugin-store": "~2.2.0", "@tauri-apps/plugin-stronghold": "~2.2.0", "@tauri-apps/plugin-updater": "~2.5.0", "@tavily/core": "^0.3.1", "ai": "^4.1.45", "marked": "^15.0.7", "tailwindcss-animated": "^2.0.0", "vue": "^3.5.13", "vue-router": "4.5.0"}, "devDependencies": {"@iconify-json/line-md": "^1.2.5", "@iconify-json/mdi": "^1.2.3", "@iconify-json/qlementine-icons": "^1.2.2", "@iconify-json/solar": "^1.2.2", "@tauri-apps/cli": "^2.2.7", "@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.20", "naive-ui": "^2.41.0", "postcss": "^8.5.3", "semver": "^7.7.1", "tailwindcss": "^4.0.8", "tsx": "^4.19.3", "typescript": "~5.7.3", "unplugin-icons": "^22.1.0", "unplugin-vue-components": "^28.4.0", "vfonts": "^0.1.0", "vite": "^6.1.1", "vue-tsc": "^2.2.2", "zod": "^3.24.2"}}