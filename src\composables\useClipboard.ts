import { ref, computed } from 'vue';
import { writeText, readText, writeImage, readImage } from '@tauri-apps/plugin-clipboard-manager';
import type { ClipboardItem } from './useConfig';

export function useClipboard() {
    const content = ref<string>('');
    const currentItem = ref<ClipboardItem | null>(null);
    const history = ref<ClipboardItem[]>([]);

    const contentPreview = computed(() =>
        content.value.length > 2000
            ? `${content.value.slice(0, 2000)}...(超过2000字符已截断)`
            : content.value
    );

    // 生成唯一ID
    const generateId = () => `clip_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 创建剪贴板项目的预览文本
    const createPreview = (content: string, type: 'text' | 'image'): string => {
        if (type === 'image') {
            return '[图片]';
        }
        return content.length > 50 ? `${content.slice(0, 50)}...` : content;
    };

    // 读取当前剪贴板内容
    const refresh = async () => {
        try {
            // 先尝试读取文本
            const textContent = await readText();
            if (textContent && textContent.trim()) {
                content.value = textContent;
                currentItem.value = {
                    id: generateId(),
                    type: 'text',
                    content: textContent,
                    timestamp: Date.now(),
                    preview: createPreview(textContent, 'text')
                };
                return;
            }
        } catch (e) {
            console.log('No text in clipboard');
        }

        try {
            // 尝试读取图片
            const imageData = await readImage();
            if (imageData && imageData.length > 0) {
                const base64 = btoa(String.fromCharCode(...imageData));
                content.value = `data:image/png;base64,${base64}`;
                currentItem.value = {
                    id: generateId(),
                    type: 'image',
                    content: base64,
                    timestamp: Date.now(),
                    preview: createPreview('', 'image')
                };
                return;
            }
        } catch (e) {
            console.log('No image in clipboard');
        }

        // 如果都没有内容，清空
        content.value = '';
        currentItem.value = null;
    };

    // 更新剪贴板内容（文本）
    const update = async (text: string) => {
        await writeText(text);
        content.value = text;
        currentItem.value = {
            id: generateId(),
            type: 'text',
            content: text,
            timestamp: Date.now(),
            preview: createPreview(text, 'text')
        };
    };

    // 更新剪贴板内容（图片）
    const updateImage = async (base64: string) => {
        try {
            // 将base64转换为Uint8Array
            const binaryString = atob(base64);
            const bytes = new Uint8Array(binaryString.length);
            for (let i = 0; i < binaryString.length; i++) {
                bytes[i] = binaryString.charCodeAt(i);
            }

            await writeImage(bytes);
            content.value = `data:image/png;base64,${base64}`;
            currentItem.value = {
                id: generateId(),
                type: 'image',
                content: base64,
                timestamp: Date.now(),
                preview: createPreview('', 'image')
            };
        } catch (e) {
            console.error('Failed to write image to clipboard:', e);
            throw e;
        }
    };

    const stats = computed(() => ({
        totalChars: content.value.length,
        nonEmptyChars: content.value.replace(/\s/g, '').length,
        totalLines: content.value.split('\n').length,
        nonEmptyLines: content.value.split('\n').filter(line => line.trim() !== '').length,
        totalLetters: (content.value.match(/[a-zA-Z]/g) || []).length,
        totalWords: (content.value.match(/\b\w+\b/g) || []).length,
        // biome-ignore lint/suspicious/noControlCharactersInRegex: <explanation>
        nonAsciiChars: (content.value.match(/[^\x00-\x7F]/g) || []).length,
        totalDigits: (content.value.match(/\d/g) || []).length,
        totalPunctuation: (content.value.match(/[.,\/#!$%\^&\*;:{}=\-_`~()]/g) || []).length,
        totalSpaces: (content.value.match(/\s/g) || []).length,
        totalChineseChars: (content.value.match(/[\u4e00-\u9fa5]/g) || []).length,
        totalUppercase: (content.value.match(/[A-Z]/g) || []).length,
        totalLowercase: (content.value.match(/[a-z]/g) || []).length,
        longestLine: Math.max(...content.value.split('\n').map(line => line.length))
    }));

    // 历史记录管理
    const addToHistory = (item: ClipboardItem, maxItems: number = 10) => {
        // 检查是否已存在相同内容
        const existingIndex = history.value.findIndex(h =>
            h.type === item.type && h.content === item.content
        );

        if (existingIndex >= 0) {
            // 如果存在，移除旧的并添加到最前面
            history.value.splice(existingIndex, 1);
        }

        // 添加到历史记录最前面
        history.value.unshift(item);

        // 限制历史记录数量
        if (history.value.length > maxItems) {
            history.value = history.value.slice(0, maxItems);
        }
    };

    const loadHistory = (historyData: ClipboardItem[]) => {
        history.value = historyData || [];
    };

    const clearHistory = () => {
        history.value = [];
    };

    const removeFromHistory = (id: string) => {
        const index = history.value.findIndex(item => item.id === id);
        if (index >= 0) {
            history.value.splice(index, 1);
        }
    };

    const selectFromHistory = async (item: ClipboardItem) => {
        if (item.type === 'text') {
            await update(item.content);
        } else if (item.type === 'image') {
            await updateImage(item.content);
        }
    };

    return {
        content,
        contentPreview,
        currentItem,
        history,
        stats,
        refresh,
        update,
        updateImage,
        addToHistory,
        loadHistory,
        clearHistory,
        removeFromHistory,
        selectFromHistory
    };
}
