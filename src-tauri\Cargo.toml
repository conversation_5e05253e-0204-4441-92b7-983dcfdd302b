[package]
name = "pasteme"
version = "1.2.4"
description = "A simple clipboard manager"
authors = ["CKylinMC"]
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
# The `_lib` suffix may seem redundant but it is necessary
# to make the lib name unique and wouldn't conflict with the bin name.
# This seems to be only an issue on Windows, see https://github.com/rust-lang/cargo/issues/8519
name = "pasteme_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2", features = [] }

[dependencies]
tauri = { version = "2.3.1", features = ["tray-icon"] }
tauri-plugin-opener = "2.2.6"
serde = { version = "1.0.218", features = ["derive"] }
serde_json = "1.0.140"
tauri-plugin-clipboard-manager = "2"
tauri-plugin-store = "2"
tauri-plugin-process = "2"
enigo = "0.3.0"
tauri-plugin-http = { version = "2", features = ["unsafe-headers"] }

[target.'cfg(not(any(target_os = "android", target_os = "ios")))'.dependencies]
tauri-plugin-autostart = "2"
tauri-plugin-global-shortcut = "2"
tauri-plugin-single-instance = "2.2.2"
tauri-plugin-updater = "2.5.1"
tauri-plugin-cors-fetch = "3.1.0"
